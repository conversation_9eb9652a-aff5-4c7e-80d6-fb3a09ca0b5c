'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { QuestionI } from '../../types';
import AnswerIcon from '../../../public/assets/svg/Answer';
import FollowIcon from '../../../public/assets/svg/Follow';
import MoreOptionsIcon from '../../../public/assets/svg/MoreOptions';

type QuestionCardPropsI = {
  question: QuestionI;
  onAnswer?: (questionId: string) => void;
  onFollow?: (questionId: string) => void;
  onPass?: (questionId: string) => void;
};

const QuestionCard = ({ question, onAnswer, onFollow, onPass }: QuestionCardPropsI) => {
  const [isFollowing, setIsFollowing] = useState(question.isFollowing);

  const handleFollow = () => {
    setIsFollowing(!isFollowing);
    onFollow?.(question.id);
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return `${Math.floor(diffInHours / 168)}w ago`;
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 mb-4 hover:shadow-md transition-shadow duration-200">
      {/* Header with close button */}
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <h2 className="text-lg font-medium text-gray-900 leading-tight mb-3 hover:text-blue-600 cursor-pointer">
            {question.title}
          </h2>
        </div>
        <button className="ml-4 p-1 hover:bg-gray-100 rounded-full transition-colors duration-200">
          <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      </div>

      {/* Content */}
      {question.content && (
        <div className="mb-4">
          <p className="text-gray-700 leading-relaxed">{question.content}</p>
        </div>
      )}

      {/* Images */}
      {question.images && question.images.length > 0 && (
        <div className="mb-4">
          <div className="grid grid-cols-1 gap-3">
            {question.images.map((image, index) => (
              <div key={index} className="relative rounded-lg overflow-hidden">
                <Image
                  src={image}
                  alt={`Question image ${index + 1}`}
                  width={600}
                  height={300}
                  className="w-full h-auto object-cover"
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Stats */}
      <div className="flex items-center text-sm text-gray-500 mb-4">
        <span className="mr-4">
          {question.answerCount === 0 ? 'No answer yet' : `${question.answerCount} answer${question.answerCount > 1 ? 's' : ''}`}
        </span>
        <span className="mr-4">•</span>
        <span>{question.lastActivity}</span>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {/* Answer Button */}
          <button
            onClick={() => onAnswer?.(question.id)}
            className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-gray-100 rounded-full transition-colors duration-200"
          >
            <AnswerIcon size={16} className="text-gray-600" />
            <span>Answer</span>
          </button>

          {/* Follow Button */}
          <button
            onClick={handleFollow}
            className={`flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-full transition-colors duration-200 ${
              isFollowing
                ? 'text-blue-700 bg-blue-50 hover:bg-blue-100'
                : 'text-gray-700 bg-gray-50 hover:bg-gray-100'
            }`}
          >
            <FollowIcon size={16} className={isFollowing ? 'text-blue-600' : 'text-gray-600'} />
            <span>{isFollowing ? 'Following' : 'Follow'}</span>
            <span className="text-xs">•</span>
            <span className="text-xs">{question.followCount}</span>
          </button>

          {/* Pass Button */}
          <button
            onClick={() => onPass?.(question.id)}
            className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-gray-100 rounded-full transition-colors duration-200"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
            <span>Pass</span>
          </button>
        </div>

        {/* More Options */}
        <button className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200">
          <MoreOptionsIcon size={20} className="text-gray-400" />
        </button>
      </div>

      {/* Tags */}
      {question.tags && question.tags.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-4 pt-4 border-t border-gray-100">
          {question.tags.map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700 hover:bg-blue-100 cursor-pointer transition-colors duration-200"
            >
              {tag}
            </span>
          ))}
        </div>
      )}
    </div>
  );
};

export default QuestionCard;
