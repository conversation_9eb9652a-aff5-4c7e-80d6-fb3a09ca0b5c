'use client';

import React from 'react';
import Image from 'next/image';
import { UserI } from '../../types';
import { CONFIG } from '../../constants/config';

type ProfileSidebarPropsI = {
  user: UserI;
};

const ProfileSidebar = ({ user }: ProfileSidebarPropsI) => {
  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
      {/* Cover Image */}
      <div className="h-16 bg-gradient-to-r from-blue-500 to-blue-600 relative">
        {user.coverImage && (
          <Image
            src={user.coverImage}
            alt="Cover"
            fill
            className="object-cover"
          />
        )}
      </div>

      {/* Profile Content */}
      <div className="px-4 pb-4 -mt-8 relative">
        {/* Avatar */}
        <div className="flex justify-center mb-4">
          <div className="relative">
            <Image
              src={user.avatar}
              alt={user.name}
              width={72}
              height={72}
              className="w-18 h-18 rounded-full border-4 border-white object-cover"
            />
            {user.isVerified && (
              <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}
          </div>
        </div>

        {/* User Info */}
        <div className="text-center mb-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-1">{user.name}</h2>
          <p className="text-sm text-gray-600 mb-1">{user.title}</p>
          <p className="text-xs text-gray-500">{user.company} • {user.location}</p>
        </div>

        {/* Bio */}
        {user.bio && (
          <div className="mb-4">
            <p className="text-sm text-gray-700 leading-relaxed">{user.bio}</p>
          </div>
        )}

        {/* Divider */}
        <div className="border-t border-gray-200 my-4"></div>

        {/* Stats */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Profile viewers</span>
            <span className="text-sm font-semibold text-blue-600">{user.profileViews}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Post impressions</span>
            <span className="text-sm font-semibold text-blue-600">{user.postImpressions}</span>
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-200 my-4"></div>

        {/* Network */}
        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-2">Expand your network</p>
          <div className="flex items-center">
            <div className="w-4 h-4 bg-yellow-400 rounded-sm mr-2"></div>
            <span className="text-sm font-semibold">Reactivate Premium: 50% Off</span>
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-200 my-4"></div>

        {/* Quick Links */}
        <div className="space-y-2">
          <div className="flex items-center text-sm text-gray-600 hover:text-gray-900 cursor-pointer">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
            </svg>
            Saved items
          </div>
          <div className="flex items-center text-sm text-gray-600 hover:text-gray-900 cursor-pointer">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
            </svg>
            Groups
          </div>
          <div className="flex items-center text-sm text-gray-600 hover:text-gray-900 cursor-pointer">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z" clipRule="evenodd" />
            </svg>
            Events
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileSidebar;
