'use client';

import React, { useState, useEffect } from 'react';
import { QuestionI, UserI, LoadingStateI } from '../../types';
import { CONFIG } from '../../constants/config';
import Header from '../Header';
import ProfileSidebar from '../ProfileSidebar';
import QuestionFeed from '../QuestionFeed';
import MobileAppPromotion from '../MobileAppPromotion';
import { ProfileSidebarSkeleton, MobileAppPromotionSkeleton } from '../Shimmer';

type DashboardLayoutPropsI = {
  user?: UserI;
  initialQuestions?: QuestionI[];
  isLoading?: boolean;
  onLoadMoreQuestions?: (page: number) => Promise<QuestionI[]>;
  hasMoreQuestions?: boolean;
};

const DashboardLayout = ({
  user,
  initialQuestions = [],
  isLoading = false,
  onLoadMoreQuestions,
  hasMoreQuestions = true,
}: DashboardLayoutPropsI) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <Header />

      {/* Main Content */}
      <main 
        className="pt-4"
        style={{ paddingTop: `calc(${CONFIG.layout.headerHeight} + 1rem)` }}
      >
        <div className="max-w-screen-xl mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
            {/* Left Sidebar - Profile */}
            <aside className="lg:col-span-3">
              <div className="sticky top-20">
                {user ? (
                  <ProfileSidebar user={user} />
                ) : (
                  <ProfileSidebarSkeleton />
                )}
              </div>
            </aside>

            {/* Center - Question Feed */}
            <section className="lg:col-span-6">
              <div className="space-y-6">
                {/* Ask Question Card */}
                <div className="bg-white rounded-lg border border-gray-200 p-4">
                  <div className="flex items-center space-x-3">
                    {user && (
                      <div className="w-10 h-10 rounded-full bg-gray-200 flex-shrink-0 overflow-hidden">
                        <img
                          src={user.avatar}
                          alt={user.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                    <button className="flex-1 text-left px-4 py-3 bg-gray-50 hover:bg-gray-100 rounded-full text-gray-500 transition-colors duration-200">
                      What do you want to ask or share?
                    </button>
                  </div>
                  <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100">
                    <button className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 rounded-lg transition-colors duration-200">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9l-5.55 5.41L18.18 22 12 18.77 5.82 22l1.73-7.59L2 9l6.91-.74L12 2z"/>
                      </svg>
                      <span>Ask</span>
                    </button>
                    <button className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 rounded-lg transition-colors duration-200">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                      </svg>
                      <span>Answer</span>
                    </button>
                    <button className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 rounded-lg transition-colors duration-200">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                      <span>Post</span>
                    </button>
                  </div>
                </div>

                {/* Question Feed */}
                <QuestionFeed
                  initialQuestions={initialQuestions}
                  onLoadMore={onLoadMoreQuestions}
                  hasMore={hasMoreQuestions}
                  isLoading={isLoading}
                />
              </div>
            </section>

            {/* Right Sidebar - Mobile App Promotion */}
            <aside className="lg:col-span-3">
              <div className="sticky top-20 space-y-6">
                <MobileAppPromotion />
                
                {/* Additional Promoted Content */}
                <div className="bg-white rounded-lg border border-gray-200 p-4">
                  <h3 className="text-sm font-semibold text-gray-900 mb-3">
                    Today's Most Viewed Writers
                  </h3>
                  <div className="space-y-3">
                    {Array.from({ length: 3 }).map((_, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gray-200 rounded-full flex-shrink-0"></div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            Writer {index + 1}
                          </p>
                          <p className="text-xs text-gray-500">
                            {Math.floor(Math.random() * 1000)}K views today
                          </p>
                        </div>
                        <button className="text-xs text-blue-600 hover:text-blue-700 font-medium">
                          Follow
                        </button>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Trending Topics */}
                <div className="bg-white rounded-lg border border-gray-200 p-4">
                  <h3 className="text-sm font-semibold text-gray-900 mb-3">
                    Trending Topics
                  </h3>
                  <div className="space-y-2">
                    {['Technology', 'Psychology', 'Business', 'Science', 'Health'].map((topic, index) => (
                      <button
                        key={index}
                        className="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors duration-200"
                      >
                        {topic}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </aside>
          </div>
        </div>
      </main>
    </div>
  );
};

export default DashboardLayout;
