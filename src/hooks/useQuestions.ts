'use client';

import { useState, useCallback } from 'react';
import { QuestionI } from '../types';
import { MOCK_QUESTIONS } from '../constants/mockData';

const useQuestions = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  // Simulate API call with delay
  const simulateApiCall = (page: number): Promise<QuestionI[]> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Generate more mock questions for pagination
        const questionsPerPage = 5;
        const startIndex = (page - 1) * questionsPerPage;
        
        if (startIndex >= 20) { // Simulate end of data after 20 questions
          resolve([]);
          return;
        }

        const newQuestions: QuestionI[] = Array.from({ length: Math.min(questionsPerPage, 20 - startIndex) }, (_, index) => {
          const questionIndex = startIndex + index;
          const baseQuestion = MOCK_QUESTIONS[questionIndex % MOCK_QUESTIONS.length];
          
          return {
            ...baseQuestion,
            id: `question-${questionIndex + 1}`,
            title: `${baseQuestion.title} (Question ${questionIndex + 1})`,
            createdAt: new Date(Date.now() - questionIndex * 3600000).toISOString(), // 1 hour apart
            answerCount: Math.floor(Math.random() * 50),
            followCount: Math.floor(Math.random() * 10),
            isFollowing: Math.random() > 0.7,
          };
        });

        resolve(newQuestions);
      }, 1000); // 1 second delay to simulate network
    });
  };

  const loadMoreQuestions = useCallback(async (page: number): Promise<QuestionI[]> => {
    setIsLoading(true);
    try {
      const newQuestions = await simulateApiCall(page);
      if (newQuestions.length === 0) {
        setHasMore(false);
      }
      return newQuestions;
    } catch (error) {
      console.error('Failed to load questions:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getInitialQuestions = useCallback(async (): Promise<QuestionI[]> => {
    setIsLoading(true);
    try {
      const questions = await simulateApiCall(1);
      return questions;
    } catch (error) {
      console.error('Failed to load initial questions:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    isLoading,
    hasMore,
    loadMoreQuestions,
    getInitialQuestions,
  };
};

export default useQuestions;
