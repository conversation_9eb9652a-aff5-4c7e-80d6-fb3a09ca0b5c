'use client';

import { Logo } from '@/components';
import React from 'react';

const MobileAppPromotion = () => {
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Get the mobile app
        </h3>
        <p className="text-sm text-gray-600">
          Stay connected and get answers on the go with our mobile app.
        </p>
      </div>

      <div className="relative mb-6">
        <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl p-6 text-white">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                <Logo  width={10} height={10} />
              </div>
              <span className="font-semibold">Navicater</span>
            </div>
            <div className="text-xs opacity-75">Mobile</div>
          </div>
          
          <div className="space-y-3">
            <div className="bg-white/20 rounded-lg p-3">
              <div className="flex items-center space-x-2 mb-2">
                <div className="w-6 h-6 bg-white/30 rounded-full"></div>
                <div className="h-3 bg-white/30 rounded w-20"></div>
              </div>
              <div className="h-2 bg-white/30 rounded w-full mb-1"></div>
              <div className="h-2 bg-white/30 rounded w-3/4"></div>
            </div>
            
            <div className="bg-white/20 rounded-lg p-3">
              <div className="flex items-center space-x-2 mb-2">
                <div className="w-6 h-6 bg-white/30 rounded-full"></div>
                <div className="h-3 bg-white/30 rounded w-24"></div>
              </div>
              <div className="h-2 bg-white/30 rounded w-full mb-1"></div>
              <div className="h-2 bg-white/30 rounded w-2/3"></div>
            </div>
          </div>
        </div>
      </div>

   

      {/* Download Buttons */}
      <div className="space-y-3">
        <button className="w-full bg-black text-white rounded-lg p-3 flex items-center justify-center space-x-2 hover:bg-gray-800 transition-colors duration-200">
          <svg className="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
            <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
          </svg>
          <div className="text-left">
            <div className="text-xs">Download on the</div>
            <div className="text-sm font-semibold">App Store</div>
          </div>
        </button>

        <button className="w-full bg-black text-white rounded-lg p-3 flex items-center justify-center space-x-2 hover:bg-gray-800 transition-colors duration-200">
          <svg className="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
          </svg>
          <div className="text-left">
            <div className="text-xs">Get it on</div>
            <div className="text-sm font-semibold">Google Play</div>
          </div>
        </button>
      </div>

      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="text-center">
          <p className="text-xs text-gray-500 mb-3">Scan to download</p>
          <div className="inline-block p-2 bg-gray-100 rounded-lg">
            <div className="w-16 h-16 bg-gray-300 rounded flex items-center justify-center">
              <svg className="w-8 h-8 text-gray-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M3 11h8V3H3v8zm2-6h4v4H5V5zM3 21h8v-8H3v8zm2-6h4v4H5v-4zM13 3v8h8V3h-8zm6 6h-4V5h4v4zM19 13h2v2h-2zM13 13h2v2h-2zM15 15h2v2h-2zM13 17h2v2h-2zM15 19h2v2h-2zM17 17h2v2h-2zM17 13h2v2h-2zM19 15h2v2h-2z"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileAppPromotion;
