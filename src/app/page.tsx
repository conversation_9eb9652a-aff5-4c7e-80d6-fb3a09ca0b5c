'use client';

import React, { useState, useEffect } from 'react';
import { MOCK_USER, MOCK_QUESTIONS } from '../constants';
import useQuestions from '../hooks/useQuestions';
import { QuestionI } from '../types';
import DashboardLayout from './(authenticated)/forums/components/DashboardLayout';

export default function Home() {
  const [initialQuestions, setInitialQuestions] = useState<QuestionI[]>([]);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const { loadMoreQuestions, hasMore } = useQuestions();

  useEffect(() => {
    const loadInitialData = async () => {
      setIsInitialLoading(true);
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setInitialQuestions(MOCK_QUESTIONS);
      } catch (error) {
        console.error('Failed to load initial data:', error);
      } finally {
        setIsInitialLoading(false);
      }
    };

    loadInitialData();
  }, []);

  return (
    <DashboardLayout
      user={MOCK_USER}
      initialQuestions={initialQuestions}
      isLoading={isInitialLoading}
      onLoadMoreQuestions={loadMoreQuestions}
      hasMoreQuestions={hasMore}
    />
  );
}
